import express, { type Request, Response, NextFunction } from "express";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  const reqPath = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${reqPath} ${res.statusCode} in ${duration}ms`);
    if (capturedJsonResponse) {
      console.log(`Response: ${JSON.stringify(capturedJsonResponse)}`);
    }
  });

  next();
});

// In-memory storage for text samples
const textSamples = [
  // English samples
  {
    id: 1,
    language: "en",
    difficulty: "beginner",
    title: "Basic English Words",
    content: "The cat sat on the mat. The sun is bright today. I like to read books. Dogs are friendly animals. We eat food every day. Water is very important. Trees give us oxygen. Birds can fly high. Fish live in water. The sky is blue."
  },
  {
    id: 2,
    language: "en",
    difficulty: "intermediate",
    title: "Common English Phrases",
    content: "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet, making it perfect for typing practice. As you type each character, you will see real-time feedback showing your accuracy and speed. Focus on maintaining a steady rhythm rather than rushing through the text."
  },
  {
    id: 3,
    language: "en",
    difficulty: "advanced",
    title: "Advanced English Vocabulary",
    content: "Sophisticated vocabulary requires extensive practice and unwavering dedication to achieve remarkable proficiency. The quintessential characteristics of exemplary typing include precision, consistency, and methodical approaches to complex linguistic constructions. Extraordinary achievements necessitate perseverance through challenging circumstances."
  },
  // Chinese samples
  {
    id: 4,
    language: "zh",
    difficulty: "beginner",
    title: "基础中文词汇",
    content: "你好世界。今天天气很好。我喜欢学习中文。家人很重要。朋友是宝贵的。学习需要努力。工作要认真。生活很美好。希望明天更好。感谢大家帮助。"
  },
  {
    id: 5,
    language: "zh",
    difficulty: "intermediate",
    title: "中文句子练习",
    content: "中文打字练习需要耐心和坚持。每天练习会提高你的打字速度。汉字是中华文化的重要组成部分。学习中文不仅能够提高语言能力，还能够了解中国的历史和文化。通过不断的练习，我们可以掌握更多的词汇和表达方式。"
  },
  {
    id: 6,
    language: "zh",
    difficulty: "advanced",
    title: "高级中文表达",
    content: "中华文化博大精深，汉字承载着深厚的历史文化内涵，需要我们认真学习和传承。在全球化的今天，掌握多种语言技能显得尤为重要。通过系统性的学习和持续不断的练习，我们能够在语言表达方面达到更高的水平，从而更好地进行跨文化交流。"
  },
  // Spanish samples
  {
    id: 7,
    language: "es",
    difficulty: "beginner",
    title: "Palabras Básicas en Español",
    content: "Hola mundo. El sol brilla hoy. Me gusta leer libros. Los perros son amigables. Comemos comida todos los días. El agua es muy importante. Los árboles nos dan oxígeno. Los pájaros pueden volar alto. Los peces viven en el agua."
  },
  {
    id: 8,
    language: "es",
    difficulty: "intermediate",
    title: "Frases Comunes en Español",
    content: "El rápido zorro marrón salta sobre el perro perezoso. Esta oración contiene muchas letras del alfabeto, lo que la hace perfecta para practicar mecanografía. Mientras escribes cada carácter, verás comentarios en tiempo real que muestran tu precisión y velocidad."
  },
  {
    id: 9,
    language: "es",
    difficulty: "advanced",
    title: "Vocabulario Avanzado en Español",
    content: "El vocabulario sofisticado requiere práctica extensa y dedicación inquebrantable para lograr una competencia notable. Las características esenciales de la mecanografía ejemplar incluyen precisión, consistencia y enfoques metódicos para construcciones lingüísticas complejas."
  },
  // French samples
  {
    id: 10,
    language: "fr",
    difficulty: "beginner",
    title: "Mots de Base en Français",
    content: "Bonjour le monde. Le soleil brille aujourd'hui. J'aime lire des livres. Les chiens sont amicaux. Nous mangeons de la nourriture tous les jours. L'eau est très importante. Les arbres nous donnent de l'oxygène. Les oiseaux peuvent voler haut."
  },
  {
    id: 11,
    language: "fr",
    difficulty: "intermediate",
    title: "Phrases Communes en Français",
    content: "Le renard brun rapide saute par-dessus le chien paresseux. Cette phrase contient de nombreuses lettres de l'alphabet, ce qui la rend parfaite pour pratiquer la dactylographie. En tapant chaque caractère, vous verrez des commentaires en temps réel montrant votre précision et votre vitesse."
  },
  {
    id: 12,
    language: "fr",
    difficulty: "advanced",
    title: "Vocabulaire Avancé en Français",
    content: "L'acquisition de compétences linguistiques sophistiquées nécessite une méthodologie systématique et un dévouement inébranlable. Les caractéristiques fondamentales de l'excellence en dactylographie incluent la précision, la cohérence et des approches méthodiques des constructions linguistiques complexes."
  }
];

// Health check endpoint
app.get("/api/health", (_req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development"
  });
});

// Get text sample for specific language and difficulty
app.get("/api/text-samples/:language/:difficulty", (req, res) => {
  try {
    const { language, difficulty } = req.params;
    console.log(`Looking for text sample: language=${language}, difficulty=${difficulty}`);

    const sample = textSamples.find(
      s => s.language === language && s.difficulty === difficulty
    );

    if (!sample) {
      console.log(`No sample found for ${language}/${difficulty}`);
      return res.status(404).json({
        message: `No text sample found for language ${language} and difficulty ${difficulty}`,
        available: textSamples.map(s => `${s.language}/${s.difficulty}`)
      });
    }

    console.log(`Found sample: ${sample.title}`);
    res.json(sample);
  } catch (error) {
    console.error("Error fetching text sample:", error);
    res.status(500).json({
      message: "Failed to fetch text sample",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

// Get all text samples
app.get("/api/text-samples", (_req, res) => {
  try {
    console.log(`Returning ${textSamples.length} text samples`);
    res.json(textSamples);
  } catch (error) {
    console.error("Error fetching all text samples:", error);
    res.status(500).json({
      message: "Failed to fetch text samples",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

// Save test result (simplified for now)
app.post("/api/test-results", (req, res) => {
  try {
    console.log("Received test result:", req.body);
    // For now, just return success
    res.status(201).json({
      id: Date.now(),
      ...req.body,
      savedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error("Error saving test result:", error);
    res.status(500).json({
      message: "Failed to save test result",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

// Get test results (simplified for now)
app.get("/api/test-results", (_req, res) => {
  try {
    // Return empty array for now
    res.json([]);
  } catch (error) {
    console.error("Error fetching test results:", error);
    res.status(500).json({
      message: "Failed to fetch test results",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

// Error handling middleware
app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
  console.error("Unhandled error:", err);
  const status = err.status || err.statusCode || 500;
  const message = err.message || "Internal Server Error";
  res.status(status).json({ message, error: err.toString() });
});

// 404 handler
app.use("*", (req, res) => {
  console.log(`404 - Route not found: ${req.method} ${req.path}`);
  res.status(404).json({
    message: "Route not found",
    path: req.path,
    method: req.method
  });
});

export default app;
