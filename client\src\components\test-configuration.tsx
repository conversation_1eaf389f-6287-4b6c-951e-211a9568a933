import { Clock, Play, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { durationOptions } from "@shared/schema";
import { getTranslation } from "@/lib/i18n";

interface TestConfigurationProps {
  duration: number;
  setDuration: (duration: number) => void;
  isTestActive: boolean;
  onStart: () => void;
  onReset: () => void;
  language: string;
}

export function TestConfiguration({
  duration,
  setDuration,
  isTestActive,
  onStart,
  onReset,
  language
}: TestConfigurationProps) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6 mb-6 sm:mb-8">
      <div className="flex flex-col lg:flex-row lg:items-center gap-4">
        {/* 测试时长选择 */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-slate-500" />
            <span className="text-sm font-medium text-slate-700">{getTranslation(language, 'testDuration')}</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {durationOptions.map(option => (
              <Button
                key={option.value}
                variant={duration === option.value ? "default" : "outline"}
                size="sm"
                onClick={() => setDuration(option.value)}
                disabled={isTestActive}
                className="px-3 py-2 min-h-[44px] text-sm"
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 lg:ml-auto">
          <Button
            onClick={onStart}
            disabled={isTestActive}
            className="px-6 py-3 min-h-[44px] flex-1 sm:flex-none"
          >
            <Play className="w-4 h-4 mr-2" />
            {isTestActive ? getTranslation(language, 'testing') : getTranslation(language, 'startTest')}
          </Button>
          <Button
            variant="outline"
            onClick={onReset}
            className="px-6 py-3 min-h-[44px] flex-1 sm:flex-none"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            {getTranslation(language, 'reset')}
          </Button>
        </div>
      </div>
    </div>
  );
}
