<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 打字测试网站 API 诊断工具</h1>
    
    <div class="container">
        <h2>基础连接测试</h2>
        <button class="test-button" onclick="testHealth()">测试健康检查端点</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="container">
        <h2>文本样本API测试</h2>
        <button class="test-button" onclick="testAllSamples()">获取所有文本样本</button>
        <button class="test-button" onclick="testSpecificSample('en', 'intermediate')">英文中级样本</button>
        <button class="test-button" onclick="testSpecificSample('zh', 'beginner')">中文初级样本</button>
        <button class="test-button" onclick="testSpecificSample('es', 'advanced')">西班牙文高级样本</button>
        <button class="test-button" onclick="testSpecificSample('invalid', 'invalid')">无效样本测试</button>
        <div id="samples-result" class="result"></div>
    </div>

    <div class="container">
        <h2>测试结果API</h2>
        <button class="test-button" onclick="testSaveResult()">保存测试结果</button>
        <button class="test-button" onclick="testGetResults()">获取测试结果</button>
        <div id="results-result" class="result"></div>
    </div>

    <div class="container">
        <h2>综合诊断</h2>
        <button class="test-button" onclick="runFullDiagnosis()">运行完整诊断</button>
        <div id="diagnosis-result" class="result"></div>
    </div>

    <script>
        const BASE_URL = 'https://typingtest-iawtcd8rg-wangyingyings-projects-8c5013f0.vercel.app';
        
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }
        
        function showLoading(elementId, message = '正在测试...') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result loading';
        }
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                const data = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    status: 'NETWORK_ERROR',
                    statusText: error.message,
                    data: null
                };
            }
        }
        
        async function testHealth() {
            showLoading('health-result');
            const result = await makeRequest(`${BASE_URL}/api/health`);
            
            if (result.success) {
                showResult('health-result', 
                    `✅ 健康检查成功!\n状态: ${result.status}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                    true
                );
            } else {
                showResult('health-result', 
                    `❌ 健康检查失败!\n状态: ${result.status}\n错误: ${result.statusText}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                    false
                );
            }
        }
        
        async function testAllSamples() {
            showLoading('samples-result');
            const result = await makeRequest(`${BASE_URL}/api/text-samples`);
            
            if (result.success) {
                const samples = result.data;
                showResult('samples-result', 
                    `✅ 获取所有样本成功!\n状态: ${result.status}\n样本数量: ${samples.length}\n样本列表:\n${samples.map(s => `- ${s.language}/${s.difficulty}: ${s.title}`).join('\n')}`, 
                    true
                );
            } else {
                showResult('samples-result', 
                    `❌ 获取样本失败!\n状态: ${result.status}\n错误: ${result.statusText}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                    false
                );
            }
        }
        
        async function testSpecificSample(language, difficulty) {
            showLoading('samples-result', `正在测试 ${language}/${difficulty} 样本...`);
            const result = await makeRequest(`${BASE_URL}/api/text-samples/${language}/${difficulty}`);
            
            if (result.success) {
                const sample = result.data;
                showResult('samples-result', 
                    `✅ 获取 ${language}/${difficulty} 样本成功!\n状态: ${result.status}\n标题: ${sample.title}\n语言: ${sample.language}\n难度: ${sample.difficulty}\n内容长度: ${sample.content.length} 字符\n内容预览: ${sample.content.substring(0, 100)}...`, 
                    true
                );
            } else {
                showResult('samples-result', 
                    `❌ 获取 ${language}/${difficulty} 样本失败!\n状态: ${result.status}\n错误: ${result.statusText}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                    false
                );
            }
        }
        
        async function testSaveResult() {
            showLoading('results-result');
            const testData = {
                wpm: 45,
                accuracy: 95.5,
                language: 'en',
                difficulty: 'intermediate',
                duration: 60,
                timestamp: new Date().toISOString()
            };
            
            const result = await makeRequest(`${BASE_URL}/api/test-results`, {
                method: 'POST',
                body: JSON.stringify(testData)
            });
            
            if (result.success) {
                showResult('results-result', 
                    `✅ 保存测试结果成功!\n状态: ${result.status}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                    true
                );
            } else {
                showResult('results-result', 
                    `❌ 保存测试结果失败!\n状态: ${result.status}\n错误: ${result.statusText}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                    false
                );
            }
        }
        
        async function testGetResults() {
            showLoading('results-result');
            const result = await makeRequest(`${BASE_URL}/api/test-results`);
            
            if (result.success) {
                showResult('results-result', 
                    `✅ 获取测试结果成功!\n状态: ${result.status}\n结果数量: ${result.data.length}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                    true
                );
            } else {
                showResult('results-result', 
                    `❌ 获取测试结果失败!\n状态: ${result.status}\n错误: ${result.statusText}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                    false
                );
            }
        }
        
        async function runFullDiagnosis() {
            showLoading('diagnosis-result', '正在运行完整诊断...');
            
            const tests = [
                { name: '健康检查', url: `${BASE_URL}/api/health` },
                { name: '所有文本样本', url: `${BASE_URL}/api/text-samples` },
                { name: '英文中级样本', url: `${BASE_URL}/api/text-samples/en/intermediate` },
                { name: '中文初级样本', url: `${BASE_URL}/api/text-samples/zh/beginner` },
                { name: '测试结果列表', url: `${BASE_URL}/api/test-results` }
            ];
            
            const results = [];
            
            for (const test of tests) {
                const result = await makeRequest(test.url);
                results.push({
                    name: test.name,
                    success: result.success,
                    status: result.status,
                    error: result.success ? null : result.statusText
                });
            }
            
            const successful = results.filter(r => r.success).length;
            const total = results.length;
            
            let report = `🔍 完整诊断报告\n`;
            report += `===================\n`;
            report += `总测试数: ${total}\n`;
            report += `成功: ${successful}\n`;
            report += `失败: ${total - successful}\n\n`;
            
            report += `详细结果:\n`;
            results.forEach(result => {
                const status = result.success ? '✅' : '❌';
                report += `${status} ${result.name}: ${result.status}`;
                if (result.error) {
                    report += ` (${result.error})`;
                }
                report += '\n';
            });
            
            report += `\n🎯 诊断结论:\n`;
            if (successful === total) {
                report += `✅ 所有API端点都正常工作！\n`;
                report += `✅ 问题可能在前端代码或网络请求配置\n`;
                report += `✅ 建议检查前端的API调用逻辑`;
            } else {
                report += `⚠️ 有 ${total - successful} 个API端点失败\n`;
                report += `⚠️ 需要检查Vercel函数部署和配置\n`;
                report += `⚠️ 建议查看Vercel Dashboard的函数日志`;
            }
            
            showResult('diagnosis-result', report, successful === total);
        }
    </script>
</body>
</html>
